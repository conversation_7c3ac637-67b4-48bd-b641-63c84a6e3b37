<%= form_with model: [:admin, page.becomes(Page)], data: { controller: 'nested-form', nested_form_wrapper_selector_value: '.nested-form-wrapper' }, id: 'page-edit-form' do |f| %>

  <% if page.errors.any? %>
    <div class="bg-red-50 mt-3 text-red-700 p-4 rounded-sm">
      <h2 class="text-lg font-semibold"><%= pluralize(page.errors.count, "chyba") %> br<PERSON><PERSON> v uložení:</h2>
      <ul class="mt-2 list-disc list-inside">
        <% page.errors.full_messages.each do |message| %>
          <li class="text-sm"><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="mt-4">
    <div data-controller="dropdown" data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide turbo:morph@window->dropdown#reconnect" class="mt-1">
      <div>
        <div class="flex space-x-4 mt-4">
          <div class="relative <%= page.type == 'Content' || page.is_homepage? ? 'w-3/5' : 'w-full' %>">
            <div class="flex-1 focus:outline-none bg-white border border-gray-100 w-full">
              <div class="relative mx-auto px-4">
                <div class="py-4">
                  <% unless page.is_homepage? %>
                    <% if page.persisted? %>
                      <!-- Show page type as read-only for existing pages -->
                      <div class="mb-4">
                        <label class="label">Typ stránky</label>
                        <div class="mt-2 p-2 bg-gray-50 border border-gray-200 rounded-md">
                          <div class="flex items-center space-x-2">
                            <% case page.type %>
                            <% when 'Content' %>
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                              </svg>
                              <span>Obsah</span>
                            <% when 'Link' %>
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                              </svg>
                              <span>Odkaz</span>
                            <% when 'Anchor' %>
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9" />
                              </svg>
                              <span>Kotva</span>
                            <% end %>
                        </div>
                      </div>
                    <% end %>
                  <% end %>

                  <!-- Render the appropriate form based on page type -->
                  <div class="mt-4">
                    <% case page.type || 'Content' %>
                    <% when 'Content' %>
                      <%= render "admin/content/pages/types/content_form", f:, page: %>
                    <% when 'Link' %>
                      <%= render "admin/content/pages/types/link_form", f:, page: %>
                    <% when 'Anchor' %>
                      <%= render "admin/content/pages/types/anchor_form", f:, page: %>
                    <% else %>
                      <%= render "admin/content/pages/types/content_form", f:, page: %>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <% if page.type == 'Content' || page.is_homepage? %>
            <div class="w-2/5">
              <div class="w-full bg-white p-4 border border-gray-100">
                <h6 class="form-section font-medium">SEO</h6>

                <div class="mt-4 bg-white">
                  <div>
                    <label class="label">
                      Meta title
                    </label>
                    <div class="mt-1">
                      <%= f.text_field :meta_title, class: "input" %>
                    </div>
                  </div>
                </div>

                <div class="mt-4 bg-white">
                  <div>
                    <label class="label">
                      Meta description
                    </label>
                    <div class="mt-1">
                      <%= f.text_area :meta_description, class: "textarea" %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="py-3 px-4 bg-base-200/80 border-t-2 border-primary/50">
    <div class="mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>