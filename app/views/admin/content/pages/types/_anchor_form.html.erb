<!-- Hidden field for page type -->
<%= f.hidden_field :type, value: "Anchor" %>

<div>
  <label class="label">
    Název
  </label>
  <div class="mt-1">
    <%= f.text_field :title, class: "input w-full" %>
  </div>
</div>

<div class="mt-4">
  <label class="label">
    Blok
  </label>
  <div class="mt-1">
    <% homepage = Page.homepage %>
    <% if homepage&.blocks&.any? %>
      <%= f.combobox :anchor_block_id, Page.homepage.blocks, placeholder: "Vyberte blok" %>
    <% else %>
      <div class="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
        Nejsou k dispozici žádné bloky na homepage pro vytvoření kotvy.
      </div>
    <% end %>
  </div>
</div>

<div class="mt-4">
  <div>
    <label class="label">Nadřazená stránka</label>
    <div class="mt-2 grid grid-cols-1">
      <%= f.collection_select :parent_id, @parent_pages, :id, :title, { include_blank: '-- žádná --' }, { class: 'col-start-1 row-start-1 select' } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>
</div>

<template data-nested-form-target="template">
  <%= f.fields_for :html_tags, HtmlTag.new, child_index: 'NEW_RECORD' do |html_tags_fields| %>
    <%= render "template_html_tag_form", f: html_tags_fields %>
  <% end %>
</template>

<div class="mt-4">
  <div class="text-sm font-medium text-gray-900 flex items-center space-x-1.5">
    <button type="button" data-action="nested-form#add" class="flex rounded-full items-center text-xs text-gray-700 p-0.5 hover:bg-avocado-100 bg-avocado-50 font-medium cursor-pointer">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
        <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
      </svg>
    </button>

    <span class="text-xs">Vlastní Scripty</span>
  </div>

  <dd class="mt-2 text-sm text-gray-900 sm:col-span-2 sm:mt-2">
    <ul role="list"
        class="divide-y divide-gray-100 rounded-md border border-gray-200 empty:border-0">
      <%= f.fields_for :html_tags do |html_tags_fields| %>
        <%= render "template_html_tag_form", f: html_tags_fields %>
      <% end %>

      <div data-nested-form-target="target"></div>
    </ul>
  </dd>
</div>