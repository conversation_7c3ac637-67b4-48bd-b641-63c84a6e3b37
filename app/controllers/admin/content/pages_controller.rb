module Admin
  class Content::PagesController < Content::ContentController
    before_action :set_breadcrumbs, except: :index

    helper_method :pages_by_locale

    def pages_by_locale(locale)
      Page.where(locale:).order(position: :asc).arrange
    end

    def search
      @pages = Page.all
    end

    def index
      @scopes = Page.where(scope: nil).order(position: :asc)

      add_breadcrumb "Stránky"
    end

    def new
      page_type = params[:type]&.capitalize || "Content"
      @page = Page.new(type: page_type)
      @parent_pages = Page.all

      add_breadcrumb "Nová stránka"
    end

    def create
      @page = Page.new(page_params)

      if @page.save
        redirect_to edit_admin_page_path(@page), notice: "Stránka byla ú<PERSON>ěšně vytvořena."
      else
        @parent_pages = Page.all
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @page = Page.friendly.find(params[:id])
      @parent_pages = Page.where.not(id: @page.subtree_ids)

      add_breadcrumb @page.title
    end

    def update
      @page = Page.friendly.find(params[:id])

      if @page.update(page_params)
        redirect_to edit_admin_page_path(@page), notice: "<PERSON>ránka byla ú<PERSON>ěš<PERSON>ě ul<PERSON>ž<PERSON>."
      else
        @parent_pages = Page.all

        render :edit, status: :unprocessable_entity
      end
    end

    def sort
      @page = Page.friendly.find(params[:id])

      @page.update position: params[:position].to_i
    end

    def destroy
      @page = Page.friendly.find(params[:id])
      @page.destroy

      redirect_to admin_content_media_articles_path, notice: "Stránka byla úspěšně smazána."
    end

    private

    def set_breadcrumbs
      add_breadcrumb "Stránky", admin_pages_path
    end

    def page_params
      permitted_params = params.require(:page).permit(:title, :anchor_block_id, :heading, :slug, :parent_id, :meta_title, :meta_description, :type, :link, html_tags_attributes: [:id, :name, :content, :position, :_destroy])

      permitted_params[:type] = permitted_params[:type].capitalize if permitted_params[:type].present?

      permitted_params
    end
  end
end
